# نظام حضور المجلس - محمد أبو كليب

نظام إلكتروني متطور لإدارة حضور المجلس بعد صلاة المغرب، مصمم خصيصاً لمجلس المعزب محمد أبو كليب.

## المميزات

- **تسجيل الحضور الإلكتروني**: نظام سهل وسريع لتسجيل حضور الأعضاء والضيوف
- **إدارة البيانات**: حفظ تلقائي للبيانات في المتصفح مع إمكانية الاستعادة
- **تتبع الوقت**: مراقبة أوقات الوصول وتصنيف الحضور (في الوقت/متأخر)
- **إحصائيات مفصلة**: تقارير شاملة مع رسوم بيانية تفاعلية
- **تصدير البيانات**: إمكانية تصدير التقارير بصيغة Excel وطباعتها
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **واجهة عربية**: مصممة بالكامل باللغة العربية مع دعم RTL

## الصفحات

### 1. الصفحة الرئيسية (`index.html`)
- صفحة ترحيب بالزوار
- معلومات عن المعزب محمد أبو كليب
- روابط سريعة للوصول إلى النظام

### 2. صفحة تسجيل الحضور (`majlis-attendance.html`)
- نموذج تسجيل الحضور
- عرض الإحصائيات المباشرة
- جدول الحضور التفاعلي
- إمكانية حذف وتعديل البيانات

### 3. صفحة التقارير (`majlis-report.html`)
- تقارير مفصلة مع رسوم بيانية
- تحليل أوقات الوصول
- توزيع الحضور حسب الصفة
- إحصائيات شاملة

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع Flexbox و Grid
- **JavaScript**: الوظائف التفاعلية وإدارة البيانات
- **Chart.js**: الرسوم البيانية التفاعلية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo العربي

## البيانات المحفوظة

يحفظ النظام البيانات التالية لكل مشارك:
- الاسم الكامل
- رقم الهاتف
- الصفة (عضو دائم، عضو مؤقت، ضيف، زائر)
- وقت الحضور
- حالة الحضور (في الوقت/متأخر)

## الإحصائيات المتاحة

- إجمالي عدد الحضور
- عدد الحضور في الوقت المحدد
- عدد الحضور المتأخر
- معدل الالتزام بالوقت
- توزيع الحضور حسب الصفة
- تحليل أوقات الوصول

## التخصيص

### تحديث معلومات المعزب
يمكن تحديث معلومات المعزب في الملفات التالية:
- `majlis-attendance.html` - قسم معلومات المضيف
- `majlis-report.html` - قسم ملخص الجلسة
- `index.html` - الصفحة الرئيسية

### تعديل وقت المغرب
في ملف `js/majlis-app.js`، يمكن تعديل وقت المغرب:
```javascript
function getMaghribTime() {
    const today = new Date();
    const maghrib = new Date(today);
    maghrib.setHours(18, 30, 0, 0); // تعديل الوقت هنا
    return maghrib;
}
```

## النشر على Netlify

1. **رفع الملفات**: ارفع جميع ملفات المشروع إلى Netlify
2. **الإعدادات**: ملف `netlify.toml` يحتوي على جميع الإعدادات المطلوبة
3. **الروابط المختصرة**:
   - `/attendance` → صفحة تسجيل الحضور
   - `/report` → صفحة التقارير

## المتطلبات

- متصفح حديث يدعم JavaScript
- اتصال بالإنترنت لتحميل الخطوط والأيقونات
- دعم localStorage لحفظ البيانات محلياً

## الترخيص

هذا المشروع مفتوح المصدر ومتاح تحت رخصة MIT.

## الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

