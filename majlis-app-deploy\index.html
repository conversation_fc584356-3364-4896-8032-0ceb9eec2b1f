<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">

    <!-- The name of your site that shows up in the browser tab -->
    <title>نظام حضور المجلس - محمد أبو كليب</title>

    <!-- Describe what your site is about -->
    <meta name="description" content="نظام إدارة حضور المجلس بعد المغرب - مجلس محمد أبو كليب">

    <!-- This is the small icon that shows up in the browser tab -->
    <link rel="shortcut icon" href="images/favicon.png">

    <!-- Styling for this site -->
    <link rel="stylesheet" href="css/majlis-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
      .welcome-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
      }

      .welcome-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 50px;
        text-align: center;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        max-width: 600px;
        width: 100%;
      }

      .welcome-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 30px;
      }

      .welcome-title {
        font-size: 2.5rem;
        color: #2d3748;
        margin-bottom: 20px;
        font-weight: 700;
      }

      .welcome-subtitle {
        font-size: 1.5rem;
        color: #667eea;
        margin-bottom: 15px;
        font-weight: 600;
      }

      .welcome-description {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 40px;
        line-height: 1.6;
      }

      .welcome-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .welcome-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        padding: 15px 30px;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        min-width: 200px;
        justify-content: center;
      }

      .welcome-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        text-decoration: none;
        color: white;
      }

      .welcome-btn.secondary {
        background: linear-gradient(135deg, #48bb78, #38a169);
      }

      .welcome-btn.secondary:hover {
        box-shadow: 0 10px 30px rgba(72, 187, 120, 0.4);
      }

      .host-info {
        background: #f7fafc;
        border-radius: 15px;
        padding: 25px;
        margin: 30px 0;
        border-right: 5px solid #667eea;
      }

      .host-name {
        font-size: 1.3rem;
        color: #2d3748;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .host-title {
        color: #667eea;
        font-size: 1rem;
      }
    </style>
  </head>

  <body>
    <div class="welcome-container">
      <div class="welcome-card">
        <div class="welcome-icon">
          <i class="fas fa-mosque"></i>
        </div>

        <h1 class="welcome-title">أهلاً وسهلاً</h1>
        <h2 class="welcome-subtitle">نظام حضور المجلس</h2>

        <div class="host-info">
          <div class="host-name">محمد أبو كليب</div>
          <div class="host-title">مضيف المجلس الكريم</div>
        </div>

        <p class="welcome-description">
          مرحباً بكم في نظام إدارة حضور المجلس الإلكتروني. يمكنكم تسجيل حضوركم ومتابعة إحصائيات الجلسة بكل سهولة.
          <br><br>
          <strong>وقت انعقاد المجلس:</strong> بعد صلاة المغرب مباشرة
        </p>

        <div class="welcome-buttons">
          <a href="majlis-attendance.html" class="welcome-btn">
            <i class="fas fa-user-plus"></i>
            تسجيل الحضور
          </a>
          <a href="majlis-report.html" class="welcome-btn secondary">
            <i class="fas fa-chart-bar"></i>
            عرض التقرير
          </a>
        </div>
      </div>
    </div>

    <script>
      // Auto redirect to attendance page after 5 seconds
      setTimeout(() => {
        if (confirm('هل تريد الانتقال إلى صفحة تسجيل الحضور؟')) {
          window.location.href = 'majlis-attendance.html';
        }
      }, 5000);
    </script>
  </body>
</html>