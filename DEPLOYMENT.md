# دليل نشر تطبيق حضور المجلس على Netlify

## الطريقة الأولى: النشر المباشر (Drag & Drop)

### الخطوات:

1. **افتح موقع Netlify Drop**
   - اذهب إلى: https://app.netlify.com/drop
   - أو اذهب إلى: https://netlify.com ثم اضغط على "Deploy"

2. **تحضير الملفات**
   - تأكد من وجود جميع الملفات في المجلد الحالي:
     - `index.html` (الصفحة الرئيسية)
     - `majlis-attendance.html` (صفحة تسجيل الحضور)
     - `majlis-report.html` (صفحة التقارير)
     - مجلد `css/` مع ملف `majlis-styles.css`
     - مجلد `js/` مع ملفات JavaScript
     - `netlify.toml` (إعدادات Netlify)
     - `_redirects` (إعادة التوجيه)

3. **رفع الملفات**
   - اسحب المجلد بالكامل إلى منطقة الرفع في Netlify Drop
   - أو اضغط على "Browse to upload" واختر جميع الملفات

4. **انتظار النشر**
   - سيقوم Netlify بمعالجة الملفات ونشرها تلقائياً
   - ستحصل على رابط مؤقت للموقع

5. **تخصيص الرابط (اختياري)**
   - يمكنك تغيير اسم الموقع من لوحة التحكم
   - اذهب إلى Site settings > Change site name

## الطريقة الثانية: النشر عبر Git

### المتطلبات:
- حساب GitHub أو GitLab
- حساب Netlify

### الخطوات:

1. **إنشاء مستودع Git**
   ```bash
   git init
   git add .
   git commit -m "إضافة نظام حضور المجلس"
   ```

2. **رفع إلى GitHub**
   - أنشئ مستودع جديد على GitHub
   - اربط المستودع المحلي بـ GitHub:
   ```bash
   git remote add origin https://github.com/username/majlis-attendance.git
   git push -u origin main
   ```

3. **ربط مع Netlify**
   - اذهب إلى https://app.netlify.com
   - اضغط على "New site from Git"
   - اختر GitHub واختر المستودع
   - اتبع التعليمات لإكمال النشر

## إعدادات Netlify المهمة

### ملف netlify.toml
```toml
[build]
  publish = "."
  
[[redirects]]
  from = "/*"
  to = "/majlis-attendance.html"
  status = 200
  force = false
```

### ملف _redirects
```
/attendance  /majlis-attendance.html  200
/report      /majlis-report.html      200
/*           /index.html              200
```

## الروابط المختصرة

بعد النشر، ستكون الروابط التالية متاحة:
- `your-site.netlify.app` - الصفحة الرئيسية
- `your-site.netlify.app/attendance` - تسجيل الحضور
- `your-site.netlify.app/report` - التقارير

## نصائح مهمة

1. **تأكد من الملفات**
   - تحقق من وجود جميع ملفات CSS و JavaScript
   - تأكد من صحة المسارات في HTML

2. **اختبار محلي**
   - اختبر التطبيق محلياً قبل النشر
   - استخدم: `npx http-server .` للاختبار المحلي

3. **النطاق المخصص (اختياري)**
   - يمكنك ربط نطاق مخصص من إعدادات Netlify
   - مثال: `majlis-attendance.com`

4. **الأمان**
   - Netlify يوفر HTTPS تلقائياً
   - البيانات محفوظة محلياً في متصفح المستخدم

## استكشاف الأخطاء

### مشاكل شائعة:

1. **الملفات لا تظهر**
   - تحقق من مسارات الملفات في HTML
   - تأكد من رفع جميع المجلدات

2. **الخطوط لا تعمل**
   - تحقق من اتصال الإنترنت
   - تأكد من روابط Google Fonts

3. **JavaScript لا يعمل**
   - افتح Developer Tools للتحقق من الأخطاء
   - تأكد من صحة مسارات ملفات JS

## الدعم

للحصول على المساعدة:
- وثائق Netlify: https://docs.netlify.com
- مجتمع Netlify: https://community.netlify.com
- دعم Netlify: https://www.netlify.com/support/
