/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-icon {
    font-size: 2.5rem;
    color: #667eea;
}

.header h1 {
    color: #2d3748;
    font-weight: 700;
    font-size: 2rem;
}

.time-info {
    text-align: left;
    direction: ltr;
}

.current-time {
    font-size: 1.5rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.maghrib-time {
    font-size: 1rem;
    color: #666;
}

/* Host Section */
.host-section {
    margin-bottom: 30px;
}

.host-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.host-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.host-info h2 {
    color: #667eea;
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.host-info h3 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.host-info p {
    color: #666;
    font-size: 1rem;
}

/* Form Styles */
.attendance-form-section {
    margin-bottom: 30px;
}

.form-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-card h3 {
    color: #2d3748;
    font-size: 1.5rem;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2d3748;
    font-weight: 600;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    width: 100%;
    justify-content: center;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Stats Section */
.stats-section {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h4 {
    color: #2d3748;
    font-size: 1rem;
    margin-bottom: 5px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

/* Table Styles */
.attendance-table-section {
    margin-bottom: 30px;
}

.table-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.table-header h3 {
    color: #2d3748;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.export-btn,
.print-btn {
    background: #48bb78;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.print-btn {
    background: #ed8936;
}

.export-btn:hover,
.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th,
td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

th {
    background: #f7fafc;
    color: #2d3748;
    font-weight: 600;
    position: sticky;
    top: 0;
}

tr:hover {
    background: #f7fafc;
}

.status-on-time {
    background: #48bb78;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.status-late {
    background: #f56565;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.action-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #c53030;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 15% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

.success-icon {
    font-size: 3rem;
    color: #48bb78;
    margin-bottom: 15px;
}

.modal-header h3 {
    color: #2d3748;
    font-size: 1.5rem;
}

.modal-body {
    padding: 30px;
    text-align: center;
}

.modal-body p {
    margin-bottom: 15px;
    color: #666;
}

.attendance-time {
    font-weight: 600;
    color: #667eea;
}

.modal-footer {
    padding: 20px 30px;
    text-align: center;
}

.modal-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .host-card {
        flex-direction: column;
        text-align: center;
    }
    
    .table-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .export-btn,
    .print-btn {
        width: 100%;
        justify-content: center;
    }
}
