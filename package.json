{"name": "majlis-attendance-system", "version": "1.0.0", "description": "نظام إلكتروني لإدارة حضور المجلس - محمد أبو كليب", "main": "index.html", "scripts": {"start": "npx http-server . -p 8080", "build": "echo 'No build process needed for static site'", "deploy": "netlify deploy --prod --dir .", "dev": "npx live-server ."}, "keywords": ["attendance", "maj<PERSON>", "arabic", "management", "system", "netlify"], "author": "فريق التطوير", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/majlis-attendance-system.git"}, "homepage": "https://your-site.netlify.app", "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2"}, "engines": {"node": ">=14.0.0"}}