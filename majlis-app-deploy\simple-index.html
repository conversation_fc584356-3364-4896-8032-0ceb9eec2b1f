<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حضور المجلس - محمد أبو كليب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 100%;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #fff;
        }
        h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #f0f8ff;
        }
        .host-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .host-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin: 20px 0;
            opacity: 0.9;
        }
        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            min-width: 200px;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-decoration: none;
            color: white;
        }
        .btn.primary {
            background: rgba(72, 187, 120, 0.8);
            border-color: rgba(72, 187, 120, 1);
        }
        .btn.secondary {
            background: rgba(237, 137, 54, 0.8);
            border-color: rgba(237, 137, 54, 1);
        }
        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(72, 187, 120, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(72, 187, 120, 0.5);
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            h1 {
                font-size: 2rem;
            }
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🕌</div>
        <h1>أهلاً وسهلاً</h1>
        <h2>نظام حضور المجلس</h2>
        
        <div class="host-info">
            <div class="host-name">محمد أبو كليب</div>
            <div>مضيف المجلس الكريم</div>
        </div>
        
        <div class="description">
            مرحباً بكم في نظام إدارة حضور المجلس الإلكتروني<br>
            يمكنكم تسجيل حضوركم ومتابعة إحصائيات الجلسة بكل سهولة<br><br>
            <strong>⏰ وقت انعقاد المجلس: بعد صلاة المغرب مباشرة</strong>
        </div>
        
        <div class="buttons">
            <a href="majlis-attendance.html" class="btn primary">
                📝 تسجيل الحضور
            </a>
            <a href="majlis-report.html" class="btn secondary">
                📊 عرض التقرير
            </a>
        </div>
        
        <div class="status">
            ✅ النظام يعمل بنجاح - جاهز لاستقبال الحضور
        </div>
    </div>

    <script>
        // Auto redirect after 10 seconds
        setTimeout(() => {
            if (confirm('هل تريد الانتقال إلى صفحة تسجيل الحضور؟')) {
                window.location.href = 'majlis-attendance.html';
            }
        }, 10000);
        
        console.log('✅ نظام حضور المجلس - محمد أبو كليب');
        console.log('🚀 التطبيق يعمل بنجاح!');
    </script>
</body>
</html>
