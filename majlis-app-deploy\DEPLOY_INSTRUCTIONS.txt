🚀 تعليمات النشر - نظام حضور المجلس

📁 المجلد الصحيح للنشر:
majlis-app-deploy

✅ الملفات الموجودة:
- index.html (الصفحة الرئيسية)
- majlis-attendance.html (تسجيل الحضور)
- majlis-report.html (التقارير)
- test.html (صفحة اختبار)
- netlify.toml (إعدادات Netlify)
- _redirects (الروابط المختصرة)
- مجلد css/ (التصميم)
- مجلد js/ (البرمجة)
- مجلد images/ (الصور)

🌐 خطوات النشر:
1. اذهب إلى: https://app.netlify.com/drop
2. اسحب جميع الملفات من مجلد majlis-app-deploy
3. انتظر انتهاء النشر
4. احصل على الرابط

🔗 الروابط بعد النشر:
- your-site.netlify.app → الصفحة الرئيسية
- your-site.netlify.app/test.html → صفحة الاختبار
- your-site.netlify.app/attendance → تسجيل الحضور
- your-site.netlify.app/report → التقارير

✅ للتأكد من نجاح النشر:
1. افتح الرابط الرئيسي
2. يجب أن ترى "أهلاً وسهلاً - نظام حضور المجلس"
3. يجب أن ترى اسم "محمد أبو كليب"
4. جرب الروابط المختلفة

🆘 إذا لم يعمل:
- جرب صفحة الاختبار: your-site.netlify.app/test.html
- تأكد من رفع جميع الملفات
- تحقق من رسائل الخطأ في Netlify

📞 للدعم:
راجع ملفات التعليمات الأخرى في المشروع
