// Global variables
let attendees = [];
let roleChart, timeChart;

// Initialize the report
document.addEventListener('DOMContentLoaded', function() {
    loadAttendanceData();
    initializeReport();
    generateCharts();
});

// Load attendance data from localStorage
function loadAttendanceData() {
    const savedAttendees = localStorage.getItem('majlisAttendees');
    if (savedAttendees) {
        attendees = JSON.parse(savedAttendees);
        // Convert string dates back to Date objects
        attendees.forEach(attendee => {
            attendee.arrivalTime = new Date(attendee.arrivalTime);
        });
    }
}

// Initialize report
function initializeReport() {
    // Set report date
    const today = new Date();
    document.getElementById('reportDate').textContent = today.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });
    
    // Generate statistics
    generateStatistics();
    generateRoleBreakdown();
    generateTimeAnalysis();
    generateAttendeesList();
}

// Generate statistics
function generateStatistics() {
    const total = attendees.length;
    const onTime = attendees.filter(a => a.status === 'on-time').length;
    const late = attendees.filter(a => a.status === 'late').length;
    const punctualityRate = total > 0 ? Math.round((onTime / total) * 100) : 0;
    
    document.getElementById('totalAttendeesReport').textContent = total;
    document.getElementById('earlyAttendeesReport').textContent = onTime;
    document.getElementById('lateAttendeesReport').textContent = late;
    document.getElementById('punctualityRate').textContent = punctualityRate + '%';
}

// Generate role breakdown
function generateRoleBreakdown() {
    const roleBreakdown = {};
    attendees.forEach(attendee => {
        roleBreakdown[attendee.role] = (roleBreakdown[attendee.role] || 0) + 1;
    });
    
    const container = document.getElementById('roleBreakdown');
    container.innerHTML = '';
    
    Object.entries(roleBreakdown).forEach(([role, count]) => {
        const percentage = Math.round((count / attendees.length) * 100);
        const roleItem = document.createElement('div');
        roleItem.className = 'role-item';
        roleItem.innerHTML = `
            <div class="role-info">
                <span class="role-name">${role}</span>
                <span class="role-count">${count} أشخاص</span>
            </div>
            <div class="role-bar">
                <div class="role-progress" style="width: ${percentage}%"></div>
            </div>
            <span class="role-percentage">${percentage}%</span>
        `;
        container.appendChild(roleItem);
    });
}

// Generate time analysis
function generateTimeAnalysis() {
    const timeSlots = {
        'بعد المغرب مباشرة (0-15 دقيقة)': 0,
        'خلال النصف ساعة الأولى (15-30 دقيقة)': 0,
        'بعد النصف ساعة (30-60 دقيقة)': 0,
        'متأخر جداً (أكثر من ساعة)': 0
    };
    
    const maghribTime = getMaghribTime();
    
    attendees.forEach(attendee => {
        const timeDiff = (attendee.arrivalTime - maghribTime) / (1000 * 60); // minutes
        
        if (timeDiff <= 15) {
            timeSlots['بعد المغرب مباشرة (0-15 دقيقة)']++;
        } else if (timeDiff <= 30) {
            timeSlots['خلال النصف ساعة الأولى (15-30 دقيقة)']++;
        } else if (timeDiff <= 60) {
            timeSlots['بعد النصف ساعة (30-60 دقيقة)']++;
        } else {
            timeSlots['متأخر جداً (أكثر من ساعة)']++;
        }
    });
    
    const container = document.getElementById('timeSlots');
    container.innerHTML = '';
    
    Object.entries(timeSlots).forEach(([slot, count]) => {
        const percentage = attendees.length > 0 ? Math.round((count / attendees.length) * 100) : 0;
        const slotItem = document.createElement('div');
        slotItem.className = 'time-slot-item';
        slotItem.innerHTML = `
            <div class="slot-info">
                <span class="slot-name">${slot}</span>
                <span class="slot-count">${count} أشخاص</span>
            </div>
            <div class="slot-bar">
                <div class="slot-progress" style="width: ${percentage}%"></div>
            </div>
            <span class="slot-percentage">${percentage}%</span>
        `;
        container.appendChild(slotItem);
    });
}

// Generate attendees list
function generateAttendeesList() {
    const container = document.getElementById('attendeesList');
    container.innerHTML = '';
    
    if (attendees.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد بيانات حضور لعرضها</p>';
        return;
    }
    
    // Sort attendees by arrival time
    const sortedAttendees = [...attendees].sort((a, b) => a.arrivalTime - b.arrivalTime);
    
    sortedAttendees.forEach((attendee, index) => {
        const attendeeItem = document.createElement('div');
        attendeeItem.className = 'attendee-item';
        
        const statusClass = attendee.status === 'on-time' ? 'status-success' : 'status-warning';
        const statusText = attendee.status === 'on-time' ? 'في الوقت' : 'متأخر';
        const statusIcon = attendee.status === 'on-time' ? 'fa-check-circle' : 'fa-clock';
        
        attendeeItem.innerHTML = `
            <div class="attendee-number">${index + 1}</div>
            <div class="attendee-info">
                <h4>${attendee.name}</h4>
                <p><i class="fas fa-phone"></i> ${attendee.phone}</p>
                <p><i class="fas fa-user-tag"></i> ${attendee.role}</p>
            </div>
            <div class="attendee-time">
                <span class="arrival-time">${attendee.arrivalTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</span>
                <span class="${statusClass}">
                    <i class="fas ${statusIcon}"></i>
                    ${statusText}
                </span>
            </div>
        `;
        
        container.appendChild(attendeeItem);
    });
}

// Generate charts
function generateCharts() {
    generateRoleChart();
    generateTimeChart();
}

// Generate role distribution chart
function generateRoleChart() {
    const ctx = document.getElementById('roleChart').getContext('2d');
    
    const roleData = {};
    attendees.forEach(attendee => {
        roleData[attendee.role] = (roleData[attendee.role] || 0) + 1;
    });
    
    const labels = Object.keys(roleData);
    const data = Object.values(roleData);
    const colors = ['#667eea', '#764ba2', '#48bb78', '#ed8936', '#f56565'];
    
    roleChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

// Generate time distribution chart
function generateTimeChart() {
    const ctx = document.getElementById('timeChart').getContext('2d');
    
    const timeData = {
        'في الوقت': attendees.filter(a => a.status === 'on-time').length,
        'متأخر': attendees.filter(a => a.status === 'late').length
    };
    
    timeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(timeData),
            datasets: [{
                label: 'عدد الأشخاص',
                data: Object.values(timeData),
                backgroundColor: ['#48bb78', '#f56565'],
                borderColor: ['#38a169', '#e53e3e'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// Get Maghrib time
function getMaghribTime() {
    const today = new Date();
    const maghrib = new Date(today);
    maghrib.setHours(18, 30, 0, 0);
    return maghrib;
}

// Export detailed report
function exportDetailedReport() {
    if (attendees.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // Create detailed CSV report
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "تقرير حضور مجلس محمد أبو كليب\n";
    csvContent += `التاريخ: ${new Date().toLocaleDateString('ar-SA')}\n\n`;
    csvContent += "الإحصائيات العامة:\n";
    csvContent += `إجمالي الحضور: ${attendees.length}\n`;
    csvContent += `الحضور في الوقت: ${attendees.filter(a => a.status === 'on-time').length}\n`;
    csvContent += `الحضور المتأخر: ${attendees.filter(a => a.status === 'late').length}\n\n`;
    csvContent += "تفاصيل الحضور:\n";
    csvContent += "الرقم,الاسم الكامل,رقم الهاتف,الصفة,وقت الحضور,الحالة\n";
    
    attendees.forEach((attendee, index) => {
        const row = [
            index + 1,
            attendee.name,
            attendee.phone,
            attendee.role,
            attendee.arrivalTime.toLocaleString('ar-SA'),
            attendee.status === 'on-time' ? 'في الوقت' : 'متأخر'
        ].join(',');
        csvContent += row + "\n";
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `تقرير_مجلس_محمد_ابو_كليب_${new Date().toLocaleDateString('ar-SA')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Print detailed report
function printDetailedReport() {
    window.print();
}
