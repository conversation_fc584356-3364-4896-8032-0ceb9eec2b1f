// Global variables
let attendees = [];
let attendanceCounter = 1;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    updateTime();
    setInterval(updateTime, 1000);
    
    // Add form event listener
    document.getElementById('attendanceForm').addEventListener('submit', handleAttendanceSubmission);
    
    // Load saved data from localStorage
    loadSavedData();
});

// Initialize application
function initializeApp() {
    console.log('تطبيق حضور المجلس تم تحميله بنجاح');
    updateStats();
}

// Update current time
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });
    
    document.getElementById('currentTime').textContent = timeString;
}

// Handle attendance form submission
function handleAttendanceSubmission(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const attendeeData = {
        id: attendanceCounter++,
        name: formData.get('attendeeName'),
        phone: formData.get('attendeePhone'),
        role: formData.get('attendeeRole'),
        arrivalTime: new Date(),
        status: determineAttendanceStatus()
    };
    
    // Add to attendees array
    attendees.push(attendeeData);
    
    // Update table and stats
    addAttendeeToTable(attendeeData);
    updateStats();
    
    // Save to localStorage
    saveData();
    
    // Show success modal
    showSuccessModal(attendeeData.arrivalTime);
    
    // Reset form
    event.target.reset();
}

// Determine if attendance is on time or late
function determineAttendanceStatus() {
    const now = new Date();
    const maghribTime = getMaghribTime();
    
    // If current time is within 30 minutes after Maghrib, consider it on time
    const timeDifference = (now - maghribTime) / (1000 * 60); // difference in minutes
    
    return timeDifference <= 30 ? 'on-time' : 'late';
}

// Get Maghrib time (for demo purposes, using a fixed time)
function getMaghribTime() {
    const today = new Date();
    const maghrib = new Date(today);
    maghrib.setHours(18, 30, 0, 0); // 6:30 PM
    return maghrib;
}

// Add attendee to table
function addAttendeeToTable(attendee) {
    const tableBody = document.getElementById('attendanceTableBody');
    const row = document.createElement('tr');
    
    const statusClass = attendee.status === 'on-time' ? 'status-on-time' : 'status-late';
    const statusText = attendee.status === 'on-time' ? 'في الوقت' : 'متأخر';
    
    row.innerHTML = `
        <td>${attendee.id}</td>
        <td>${attendee.name}</td>
        <td>${attendee.phone}</td>
        <td>${attendee.role}</td>
        <td>${attendee.arrivalTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</td>
        <td><span class="${statusClass}">${statusText}</span></td>
        <td>
            <button class="action-btn" onclick="removeAttendee(${attendee.id})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tableBody.appendChild(row);
}

// Remove attendee
function removeAttendee(id) {
    if (confirm('هل أنت متأكد من حذف هذا المشارك؟')) {
        attendees = attendees.filter(attendee => attendee.id !== id);
        refreshTable();
        updateStats();
        saveData();
    }
}

// Refresh table
function refreshTable() {
    const tableBody = document.getElementById('attendanceTableBody');
    tableBody.innerHTML = '';
    
    attendees.forEach(attendee => {
        addAttendeeToTable(attendee);
    });
}

// Update statistics
function updateStats() {
    const totalAttendees = attendees.length;
    const onTimeAttendees = attendees.filter(a => a.status === 'on-time').length;
    const lateAttendees = attendees.filter(a => a.status === 'late').length;
    
    document.getElementById('totalAttendees').textContent = totalAttendees;
    document.getElementById('onTimeAttendees').textContent = onTimeAttendees;
    document.getElementById('lateAttendees').textContent = lateAttendees;
}

// Show success modal
function showSuccessModal(arrivalTime) {
    const modal = document.getElementById('successModal');
    const registrationTime = document.getElementById('registrationTime');
    
    registrationTime.textContent = arrivalTime.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    
    modal.style.display = 'block';
    
    // Auto close after 3 seconds
    setTimeout(() => {
        closeModal();
    }, 3000);
}

// Close modal
function closeModal() {
    document.getElementById('successModal').style.display = 'none';
}

// Export to Excel
function exportToExcel() {
    if (attendees.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "الرقم,الاسم الكامل,رقم الهاتف,الصفة,وقت الحضور,الحالة\n";
    
    attendees.forEach(attendee => {
        const row = [
            attendee.id,
            attendee.name,
            attendee.phone,
            attendee.role,
            attendee.arrivalTime.toLocaleString('ar-SA'),
            attendee.status === 'on-time' ? 'في الوقت' : 'متأخر'
        ].join(',');
        csvContent += row + "\n";
    });
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `حضور_المجلس_${new Date().toLocaleDateString('ar-SA')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Print table
function printTable() {
    if (attendees.length === 0) {
        alert('لا توجد بيانات للطباعة');
        return;
    }
    
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

// Generate print content
function generatePrintContent() {
    const currentDate = new Date().toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    let tableRows = '';
    attendees.forEach(attendee => {
        const statusText = attendee.status === 'on-time' ? 'في الوقت' : 'متأخر';
        tableRows += `
            <tr>
                <td>${attendee.id}</td>
                <td>${attendee.name}</td>
                <td>${attendee.phone}</td>
                <td>${attendee.role}</td>
                <td>${attendee.arrivalTime.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</td>
                <td>${statusText}</td>
            </tr>
        `;
    });
    
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>جدول حضور المجلس</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; }
                .host-info { text-align: center; margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>جدول حضور المجلس</h1>
                <p>التاريخ: ${currentDate}</p>
            </div>
            <div class="host-info">
                <h2>المعزب: محمد أبو كليب</h2>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم الكامل</th>
                        <th>رقم الهاتف</th>
                        <th>الصفة</th>
                        <th>وقت الحضور</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
            <div class="footer">
                <p>إجمالي الحضور: ${attendees.length}</p>
                <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
            </div>
        </body>
        </html>
    `;
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('majlisAttendees', JSON.stringify(attendees));
    localStorage.setItem('majlisCounter', attendanceCounter.toString());
}

// Load saved data from localStorage
function loadSavedData() {
    const savedAttendees = localStorage.getItem('majlisAttendees');
    const savedCounter = localStorage.getItem('majlisCounter');
    
    if (savedAttendees) {
        attendees = JSON.parse(savedAttendees);
        // Convert string dates back to Date objects
        attendees.forEach(attendee => {
            attendee.arrivalTime = new Date(attendee.arrivalTime);
        });
        refreshTable();
    }
    
    if (savedCounter) {
        attendanceCounter = parseInt(savedCounter);
    }
    
    updateStats();
}

// Clear all data (for testing purposes)
function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
        attendees = [];
        attendanceCounter = 1;
        localStorage.removeItem('majlisAttendees');
        localStorage.removeItem('majlisCounter');
        refreshTable();
        updateStats();
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('successModal');
    if (event.target === modal) {
        closeModal();
    }
}
